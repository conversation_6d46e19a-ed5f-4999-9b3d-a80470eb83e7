{"nodes": {"flake-compat": {"flake": false, "locked": {"lastModified": 1696426674, "narHash": "sha256-kvjfFW7WAETZlt09AgDn1MrtKzP7t90Vf7vypd3OL1U=", "owner": "edols<PERSON>", "repo": "flake-compat", "rev": "0f9255e01c2351cc7d116c072cb317785dd33b33", "type": "github"}, "original": {"owner": "edols<PERSON>", "repo": "flake-compat", "type": "github"}}, "flake-parts": {"inputs": {"nixpkgs-lib": "nixpkgs-lib"}, "locked": {"lastModified": 1722555600, "narHash": "sha256-XOQkdLafnb/p9ij77byFQjDf5m5QYl9b2REiVClC+x4=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "8471fe90ad337a8074e957b69ca4d0089218391d", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "gitignore": {"inputs": {"nixpkgs": ["pre-commit-hooks-nix", "nixpkgs"]}, "locked": {"lastModified": 1709087332, "narHash": "sha256-HG2cCnktfHsKV0s4XW83gU3F57gaTljL9KNSuG6bnQs=", "owner": "hercules-ci", "repo": "gitignore.nix", "rev": "637db329424fd7e46cf4185293b9cc8c88c95394", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "gitignore.nix", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1723637854, "narHash": "sha256-med8+5DSWa2UnOqtdICndjDAEjxr5D7zaIiK4pn0Q7c=", "owner": "NixOS", "repo": "nixpkgs", "rev": "c3aa7b8938b17aebd2deecf7be0636000d62a2b9", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-lib": {"locked": {"lastModified": 1722555339, "narHash": "sha256-uFf2QeW7eAHlYXuDktm9c25OxOyCoUOQmh5SZ9amE5Q=", "type": "tarball", "url": "https://github.com/NixOS/nixpkgs/archive/a5d394176e64ab29c852d03346c1fc9b0b7d33eb.tar.gz"}, "original": {"type": "tarball", "url": "https://github.com/NixOS/nixpkgs/archive/a5d394176e64ab29c852d03346c1fc9b0b7d33eb.tar.gz"}}, "nixpkgs-stable": {"locked": {"lastModified": 1720386169, "narHash": "sha256-NGKVY4PjzwAa4upkGtAMz1npHGoRzWotlSnVlqI40mo=", "owner": "NixOS", "repo": "nixpkgs", "rev": "194846768975b7ad2c4988bdb82572c00222c0d7", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-24.05", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_2": {"locked": {"lastModified": 1719082008, "narHash": "sha256-jHJSUH619zBQ6WdC21fFAlDxHErKVDJ5fpN0Hgx4sjs=", "owner": "NixOS", "repo": "nixpkgs", "rev": "9693852a2070b398ee123a329e68f0dab5526681", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "pre-commit-hooks-nix": {"inputs": {"flake-compat": "flake-compat", "gitignore": "gitignore", "nixpkgs": "nixpkgs_2", "nixpkgs-stable": "nixpkgs-stable"}, "locked": {"lastModified": 1723803910, "narHash": "sha256-yezvUuFiEnCFbGuwj/bQcqg7RykIEqudOy/RBrId0pc=", "owner": "cachix", "repo": "pre-commit-hooks.nix", "rev": "bfef0ada09e2c8ac55bbcd0831bd0c9d42e651ba", "type": "github"}, "original": {"owner": "cachix", "repo": "pre-commit-hooks.nix", "type": "github"}}, "root": {"inputs": {"flake-parts": "flake-parts", "nixpkgs": "nixpkgs", "pre-commit-hooks-nix": "pre-commit-hooks-nix"}}}, "root": "root", "version": 7}