name: Docker
on:
  release:
    types: [published]
  push:
    branches: [ master ]
  schedule:
    - cron : '0 1 * * 0'

jobs:
  publish:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      # This is used to complete the identity challenge
      # with sigstore/fulcio when running outside of PRs.
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      # https://github.com/sigstore/cosign-installer
      - name: Install cosign
        # v3.1.2
        uses: sigstore/cosign-installer@11086d25041f77fe8fe7b9ea4e48e3b9192b8f19
        with:
          cosign-release: 'v1.13.6'

      - name: Set up QEMU for ARM emulation
        # v2.2.0
        uses: docker/setup-qemu-action@2b82ce82d56a2a04d2637cd93a637ae1b359c0a7
        with:
          platforms: all
      - name: Set up Docker Buildx
        # 2.10.0
        uses: docker/setup-buildx-action@885d1462b80bc1c1c7f0b00334ad271f09369c55

      - name: Login to Docker Hub
        # 2.2.0
        uses: docker/login-action@465a07811f14bebb1938fbed4728c6a1ff8901fc
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}
      - name: Login to GHCR
        # 2.2.0
        uses: docker/login-action@465a07811f14bebb1938fbed4728c6a1ff8901fc
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Generate Docker metadata
        id: meta
        # v4.6.0
        uses: docker/metadata-action@818d4b7b91585d195f67373fd9cb0332e31a7175
        with:
          images: |
           ghcr.io/${{ github.repository }}
           docker.io/${{ secrets.DOCKER_HUB_USERNAME }}/yt-dlp-webui
          tags: |
            type=raw,value=latest

      - name: Build and push Docker image
        id: build-and-push
        # v4.2.1
        uses: docker/build-push-action@0a97817b6ade9f46837855d676c4cca3a2471fc9
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels}}

      - name: Sign the published Docker image
        env:
          COSIGN_EXPERIMENTAL: "true"
        # This step uses the identity token to provision an ephemeral certificate
        # against the sigstore community Fulcio instance.
        run: |
          cosign sign ghcr.io/${{ github.repository }}@${{ steps.build-and-push.outputs.digest }}
          cosign sign docker.io/${{ secrets.DOCKER_HUB_USERNAME }}/yt-dlp-webui@${{ steps.build-and-push.outputs.digest }}
