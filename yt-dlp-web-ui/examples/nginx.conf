worker_processes  1;

events {
    worker_connections  1024;
}


http {
    include       mime.types;

    default_type  application/octet-stream;

    sendfile            on;
    keepalive_timeout   65;

    gzip  on;

    server {
        listen       80;
        server_name  localhost;

        location ~/yt-dlp/(.*)$ {
            proxy_pass          http://127.0.0.1:3033/$1;
            proxy_set_header    X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header    Host $http_host;
            proxy_http_version  1.1;
            proxy_set_header    Upgrade $http_upgrade;
            proxy_set_header    Connection "upgrade";

            client_max_body_size    20000m;
            proxy_connect_timeout   3000;
            proxy_send_timeout      3000;
            proxy_read_timeout      3000;
            send_timeout            3000;
        }
    }
 }
