package middlewares

import "net/http"

// Middleware for applying CORS policy for ALL hosts and for
// allowing ALL request headers.
func CORS(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
		w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "*")
		w.<PERSON>er().Set("Access-Control-Allow-Credentials", "true")
		next.ServeHTTP(w, r)
	})
}
