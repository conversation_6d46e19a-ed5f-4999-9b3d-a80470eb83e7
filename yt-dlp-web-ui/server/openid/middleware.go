package openid

import "net/http"

func Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		token, err := r.<PERSON>("oid-token")
		if err != nil {
			http.Error(w, err.<PERSON><PERSON>r(), http.StatusBadRequest)
			return
		}

		if _, err := verifier.Verify(r.Context(), token.Value); err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		next.ServeHTTP(w, r)
	})
}
