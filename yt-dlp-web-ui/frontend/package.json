{"name": "yt-dlp-webui", "version": "3.2.5", "description": "Frontend compontent of yt-dlp-webui", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build"}, "type": "module", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "GPL-3.0-only", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.0.13", "@fontsource/roboto-mono": "^5.0.18", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "fp-ts": "^2.16.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.23.1", "react-virtuoso": "^4.7.11", "jotai": "^2.10.3", "rxjs": "^7.8.1"}, "devDependencies": {"@modyfi/vite-plugin-yaml": "^1.1.0", "@types/node": "^20.14.2", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "@types/react-helmet": "^6.1.11", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react-swc": "^3.7.2", "typescript": "^5.7.2", "vite": "^6.0.3"}}