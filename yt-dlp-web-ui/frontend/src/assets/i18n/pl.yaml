keys:
  urlInput: <PERSON><PERSON> URL YouTube lub innej obsługiwanej usługi
  statusTitle: Status
  startButton: Początek
  statusReady: Gotowy
  abortAllButton: Anuluj wszystko
  updateBinButton: Zaktualizuj plik binarny yt-dlp
  darkThemeButton: Ciemny motyw
  lightThemeButton: Światło motyw
  settingsAnchor: Ustawienia
  serverAddressTitle: Adres serwera
  serverPortTitle: Port
  extractAudioCheckbox: Wyodr<PERSON>bnij dźwięk
  noMTimeCheckbox: Nie ustawiaj czasu modyfikacji pliku
  bgReminder: Po zamknięciu tej strony pobieranie będzie kontynuowane w tle. 
  toastConnected: 'Połączony z '
  toastUpdated: Zaktualizowano plik binarny yt-dlp!
  formatSelectionEnabler: Aktywuj wybór formatów wideo/audio
  themeSelect: 'Motyw'
  languageSelect: '<PERSON><PERSON><PERSON><PERSON>'
  overridesAnchor: <PERSON>rzede<PERSON><PERSON>j
  pathOverrideOption: Aktywuj zastąpienie ścieżki źródłowej
  filenameOverrideOption: Aktywuj zastępowanie nazwy pliku źródłowego
  autoFileExtensionOption: Automatyczne rozszerzenie pliku
  customFilename: Wprowadź nazwę pliku (pozostaw puste, aby użyć nazwy domyślnej)
  customPath: Ustaw ścieżkę
  customArgs: Uwzględnij konfigurowalne argumenty yt-dlp (wielka moc = wielka odpowiedzialność)
  customArgsInput: Niestandardowe argumenty yt-dlp
  rpcConnErr: Wystąpił błąd podczas łączenia z serwerem RPC
  splashText: Brak aktywnych pobrań
  archiveTitle: Archiwum
  clipboardAction: Adres URL zostanie skopiowany do schowka
  playlistCheckbox: Download playlist (it will take time, after submitting you may even close this window)
  servedFromReverseProxyCheckbox: Is behind a reverse proxy subfolder
  newDownloadButton: New download
  homeButtonLabel: Home
  archiveButtonLabel: Archive
  settingsButtonLabel: Settings
  rpcAuthenticationLabel: RPC authentication
  themeTogglerLabel: Theme toggler
  loadingLabel: Loading...
  appTitle: App title
  savedTemplates: Saved templates
  templatesEditor: Templates editor
  templatesEditorNameLabel: Template name
  templatesEditorContentLabel: Template content
  logsTitle: 'Logs'
  awaitingLogs: 'Awaiting logs...'
  bulkDownload: 'Download files in a zip archive'
  templatesReloadInfo: To register a new template it might need a page reload.
  livestreamURLInput: Livestream URL
  livestreamStatusWaiting: Waiting/Wait start
  livestreamStatusDownloading: Downloading
  livestreamStatusCompleted: Completed
  livestreamStatusErrored: Errored
  livestreamStatusUnknown: Unknown
  livestreamNoMonitoring: No livestreams monitored
  livestreamDownloadInfo: |
    This will monitor yet to start livestream. Each process will be executed with --wait-for-video 10.
    If an already started livestream is provided it will be still downloaded but its progress will not be tracked.
    Once started the livestream will be migrated to the downloads page.
  livestreamExperimentalWarning: This feature is still experimental. Something might break!
  accentSelect: 'Accent'
  urlBase: URL base, for reverse proxy support (subdir), defaults to empty
  rpcPollingTimeTitle: RPC polling time
  rpcPollingTimeDescription: A lower interval results in higher CPU usage (server and client side)
  generalDownloadSettings: 'General Download Settings'
  deleteCookies: Delete Cookies
  noFilesFound: 'No Files Found'
  tableView: 'Table View'
  deleteSelected: 'Delete selected'
  subscriptionsButtonLabel: 'Subscriptions'
  subscriptionsEmptyLabel: 'No subscriptions'
  subscriptionsURLInput: 'Channel URL'
  subscriptionsInfo: |
    Subscribes to a defined channel. Only the last video will be downloaded.
    The monitor job will be scheduled/triggered by a defined cron expression (defaults to every 5 minutes if left blank).
  cronExpressionLabel: 'Cron expression'
  editButtonLabel: 'Edit'
  newSubscriptionButton: New subscription