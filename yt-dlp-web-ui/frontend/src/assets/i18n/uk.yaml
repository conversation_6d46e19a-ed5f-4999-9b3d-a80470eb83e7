keys:
  urlInput: URL-адреса YouTube або будь-якого іншого підтримуваного сервісу
  statusTitle: Статус
  startButton: Почати
  statusReady: Готово
  abortAllButton: Перервати все 
  updateBinButton: Оновити бінарний файл yt-dlp
  darkThemeButton: Темна тема 
  lightThemeButton: Світла тема 
  settingsAnchor: Налаштування 
  serverAddressTitle: Адреса сервера
  serverPortTitle: Порт 
  extractAudioCheckbox: Витягти аудіо 
  noMTimeCheckbox: Не встановлювати час модифікації файлу
  bgReminder: Як тільки ви закриєте цю сторінку, завантаження продовжиться у фоновому режимі. 
  toastConnected: 'Підключений до '
  toastUpdated: Бінарний файл yt-dlp оновлено!
  formatSelectionEnabler: Активувати вибір відео/аудіо форматів
  themeSelect: 'Тема'
  languageSelect: 'Мова'
  overridesAnchor: Перевизначити
  pathOverrideOption: Активувати перевизначення вихідного шляху
  filenameOverrideOption: Активувати перевизначення імені вихідного файлу
  autoFileExtensionOption: Автоматичне додавання розширення файлу
  customFilename: Введіть ім'я файлу (залишіть порожнім, щоб використовувати значення за замовчуванням)
  customPath: Задати шлях
  customArgs: Включити аргументи, що настроюються yt-dlp (велика сила = велика відповідальність)
  customArgsInput: Користувальницькі аргументи yt-dlp
  rpcConnErr: Помилка при підключенні до сервера RPC
  splashText: Немає активних завантажень
  archiveTitle: Архів
  clipboardAction: URL скопійовано в буфер обміну
  playlistCheckbox: Download playlist (it will take time, after submitting you may even close this window)
  servedFromReverseProxyCheckbox: Is behind a reverse proxy subfolder
  newDownloadButton: New download
  homeButtonLabel: Home
  archiveButtonLabel: Archive
  settingsButtonLabel: Settings
  rpcAuthenticationLabel: RPC authentication
  themeTogglerLabel: Theme toggler
  loadingLabel: Loading...
  appTitle: App title
  savedTemplates: Saved templates
  templatesEditor: Templates editor
  templatesEditorNameLabel: Template name
  templatesEditorContentLabel: Template content
  logsTitle: 'Logs'
  awaitingLogs: 'Awaiting logs...'
  bulkDownload: 'Download files in a zip archive'
  templatesReloadInfo: To register a new template it might need a page reload.
  livestreamURLInput: Livestream URL
  livestreamStatusWaiting: Waiting/Wait start
  livestreamStatusDownloading: Downloading
  livestreamStatusCompleted: Completed
  livestreamStatusErrored: Errored
  livestreamStatusUnknown: Unknown
  livestreamNoMonitoring: No livestreams monitored
  livestreamDownloadInfo: |
    This will monitor yet to start livestream. Each process will be executed with --wait-for-video 10.
    If an already started livestream is provided it will be still downloaded but its progress will not be tracked.
    Once started the livestream will be migrated to the downloads page.
  livestreamExperimentalWarning: This feature is still experimental. Something might break!
  accentSelect: 'Accent'
  urlBase: URL base, for reverse proxy support (subdir), defaults to empty
  rpcPollingTimeTitle: RPC polling time
  rpcPollingTimeDescription: A lower interval results in higher CPU usage (server and client side)
  generalDownloadSettings: 'General Download Settings'
  deleteCookies: Delete Cookies
  noFilesFound: 'No Files Found'
  tableView: 'Table View'
  deleteSelected: 'Delete selected'
  subscriptionsButtonLabel: 'Subscriptions'
  subscriptionsEmptyLabel: 'No subscriptions'
  subscriptionsURLInput: 'Channel URL'
  subscriptionsInfo: |
    Subscribes to a defined channel. Only the last video will be downloaded.
    The monitor job will be scheduled/triggered by a defined cron expression (defaults to every 5 minutes if left blank).
  cronExpressionLabel: 'Cron expression'
  editButtonLabel: 'Edit'
  newSubscriptionButton: New subscription