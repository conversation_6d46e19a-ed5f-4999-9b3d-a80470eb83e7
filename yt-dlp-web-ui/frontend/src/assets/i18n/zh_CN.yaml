keys:
  urlInput: 视频 URL
  statusTitle: 状态
  statusReady: 就绪
  selectFormatButton: 选择格式
  startButton: 开始
  abortAllButton: 全部中止
  updateBinButton: 更新 yt-dlp 可执行文件
  darkThemeButton: 黑暗主题
  lightThemeButton: 明亮主题
  settingsAnchor: 设置
  serverAddressTitle: 服务器地址
  serverPortTitle: 端口
  extractAudioCheckbox: 提取音频
  noMTimeCheckbox: 不设置文件修改时间
  bgReminder: 关闭页面后，下载会继续在后台运行。
  toastConnected: '已连接到 '
  toastUpdated: 已更新 yt-dlp 可执行文件！
  formatSelectionEnabler: 启用视频/音频格式选择
  themeSelect: '主题'
  languageSelect: '语言'
  overridesAnchor: 覆盖
  pathOverrideOption: 启用输出路径覆盖
  filenameOverrideOption: 启用输出文件名覆盖
  autoFileExtensionOption: 自动文件扩展名
  customFilename: 自定义文件名（留空使用默认值）
  customPath: 自定义路径
  customArgs: 启用自定义 yt-dlp 参数（能力越大 = 责任越大）
  customArgsInput: 自定义 yt-dlp 参数
  rpcConnErr: 连接 RPC 服务器发生错误
  splashText: 没有正在进行的下载
  archiveTitle: 归档
  clipboardAction: 复制 URL 到剪贴板
  playlistCheckbox: 下载播放列表（可能需要一段时间，提交后可以关闭页面等待）
  restartAppMessage: 需要刷新页面才能生效
  servedFromReverseProxyCheckbox: 处于反向代理的子目录后
  newDownloadButton: 新下载
  homeButtonLabel: 主页
  archiveButtonLabel: 归档
  settingsButtonLabel: 设置
  rpcAuthenticationLabel: RPC 身份验证
  themeTogglerLabel: 主题切换
  loadingLabel: 正在加载…
  appTitle: App 标题
  savedTemplates: 保存模板
  templatesEditor: 模板编辑器
  templatesEditorNameLabel: 模板名称
  templatesEditorContentLabel: 模板内容
  logsTitle: '日志'
  awaitingLogs: '正在等待日志…'
  bulkDownload: '下载 zip 压缩包中的文件'
  templatesReloadInfo: To register a new template it might need a page reload.
  livestreamURLInput: 直播 URL
  livestreamStatusWaiting: 等待直播开始
  livestreamStatusDownloading: 下载中
  livestreamStatusCompleted: 已完成
  livestreamStatusErrored: 发生错误
  livestreamStatusUnknown: 未知
  livestreamNoMonitoring: No livestreams monitored
  livestreamDownloadInfo: |
    本功能将会监控即将开始的直播流，每个进程都会传入参数：--wait-for-video 10 （重试间隔10秒）
    如果直播已经开始，那么依然可以下载，但是不会记录下载进度。
    直播开始后，将会转移到下载页面
  livestreamExperimentalWarning: 实验性功能，可能存在未知Bug，请谨慎使用
  accentSelect: 'Accent'
  urlBase: URL base, for reverse proxy support (subdir), defaults to empty
  rpcPollingTimeTitle: RPC polling time
  rpcPollingTimeDescription: A lower interval results in higher CPU usage (server and client side)
  generalDownloadSettings: 'General Download Settings'
  deleteCookies: Delete Cookies
  noFilesFound: 'No Files Found'
  tableView: 'Table View'
  deleteSelected: 'Delete selected'
  subscriptionsButtonLabel: 'Subscriptions'
  subscriptionsEmptyLabel: 'No subscriptions'
  subscriptionsURLInput: 'Channel URL'
  subscriptionsInfo: |
    Subscribes to a defined channel. Only the last video will be downloaded.
    The monitor job will be scheduled/triggered by a defined cron expression (defaults to every 5 minutes if left blank).
  cronExpressionLabel: 'Cron expression'
  editButtonLabel: 'Edit'
  newSubscriptionButton: New subscription