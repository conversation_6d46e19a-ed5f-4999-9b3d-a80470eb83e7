keys:
  urlInput: Video URL
  statusTitle: Status
  statusReady: Bereit
  selectFormatButton: Format auswählen
  startButton: Start
  abortAllButton: Alle Abbrechen
  updateBinButton: yt-dlp Binärdatei aktualisieren
  darkThemeButton: Dunkler Modus
  lightThemeButton: Heller Modus
  settingsAnchor: Einstellungen
  serverAddressTitle: Adresse des Servers
  serverPortTitle: Port
  extractAudioCheckbox: Audio extrahieren
  noMTimeCheckbox: Datei-Änderungszeitpunkt nicht festlegen
  bgReminder: Sobald Sie diese Se<PERSON> schließen, wird der Download im Hintergrund fortgesetzt.
  toastConnected: 'Verbunden mit '
  toastUpdated: yt-dlp Binärdatei aktualisiert!
  formatSelectionEnabler: Video/Audio Format auswählbar
  themeSelect: 'Modus'
  languageSelect: 'Sprache'
  overridesAnchor: Überschreibungen
  pathOverrideOption: Ausgabe-Pfad Überschreibung aktivieren
  filenameOverrideOption: Ausgabe-Dateiname Überschreibung aktivieren
  autoFileExtensionOption: Dateierweiterung automatisch hinzufügen
  customFilename: Benutzerdefinierter Dateiname (Leer lassen um Standardwert zu nutzen)
  customPath: Benutzerdefinierter Pfad
  customArgs: Benutzerdefinierte yt-dlp Argumente aktivieren (Auf viel Macht folgt große Verantwortung)
  customArgsInput: Benutzerdefinierte yt-dlp Argumente
  rpcConnErr: Fehler beim Verbinden mit RPC Server
  splashText: Keine aktiven Downloads
  archiveTitle: Archiv
  clipboardAction: URL in Zwischenablage kopiert
  playlistCheckbox: Playlist herunterladen (es wird einige Zeit dauern, nach dem Absenden können Sie dieses Fenster schließen)
  restartAppMessage: Erfordert ein Neuladen der Seite, um wirksam zu werden
  servedFromReverseProxyCheckbox: Ist hinter einem Reverse Proxy Unterordner
  newDownloadButton: Neuer Download
  homeButtonLabel: Home
  archiveButtonLabel: Archiv
  settingsButtonLabel: Einstellungen
  rpcAuthenticationLabel: RPC Authentifizierung
  themeTogglerLabel: Modus Umschalter
  loadingLabel: Lädt...
  appTitle: App Titel
  savedTemplates: Gespeicherte Vorlage
  templatesEditor: Vorlageneditor
  templatesEditorNameLabel: Vorlagen Name
  templatesEditorContentLabel: Vorlagen Inhalt
  logsTitle: 'Logausgabe'
  awaitingLogs: 'Warte auf Log ...'
  bulkDownload: 'Alles in einem ZIP-Archiv herunterladen'
  rpcPollingTimeTitle: RPC-Abfragezeit
  rpcPollingTimeDescription: Ein kürzerer Intervall führt zu einer höheren CPU-Auslastung (Server- und Clientseite)
  templatesReloadInfo: Um eine neue Vorlage zu registrieren, muss die Seite möglicherweise neu geladen werden.
  livestreamURLInput: Livestream URL
  livestreamStatusWaiting: Warte auf Start
  livestreamStatusDownloading: Herunterladen
  livestreamStatusCompleted: Abgeschlossen
  livestreamStatusErrored: Fehlerhaft
  livestreamStatusUnknown: Status unbekannt
  livestreamNoMonitoring: Aktuell wird kein Livestream überwacht
  livestreamDownloadInfo: |
    Damit wird der noch nicht gestartete Livestream überwacht. Jeder Prozess wird mit --wait-for-video 10 ausgeführt.
    Wenn ein bereits gestarteter Livestream vorhanden ist, wird er zwar heruntergeladen, aber sein Fortschritt wird nicht verfolgt.
    Sobald der Livestream gestartet ist, wird er auf der Download-Seite angezeigt.
  livestreamExperimentalWarning: Dieses Feature ist aktuell noch experimentell, sei vorsichtig, denn es könnte sein, dass etwas nicht genau funktioniert!
  accentSelect: 'Farbtöne'
  urlBase: URL-Basis für Reverse-Proxy-Unterstützung (Unterverzeichnis), standardmäßig leer
  generalDownloadSettings: 'Allgemeine Download Einstellungen'
  deleteCookies: 'Cookies löschen'
  noFilesFound: 'Keine Dateien gefunden'
  tableView: 'Tabellenansicht'
  deleteSelected: 'Ausgewählte löschen'
  subscriptionsButtonLabel: 'Subscriptions'
  subscriptionsEmptyLabel: 'No subscriptions'
  subscriptionsURLInput: 'Channel URL'
  subscriptionsInfo: |
    Subscribes to a defined channel. Only the last video will be downloaded.
    The monitor job will be scheduled/triggered by a defined cron expression (defaults to every 5 minutes if left blank).
  cronExpressionLabel: 'Cron expression'
  editButtonLabel: 'Edit'
  newSubscriptionButton: New subscription